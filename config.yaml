
# see https://github.com/sigoden/aichat/blob/main/config.example.yaml
model: newapi:gpt-4o  # 指定默认模型

clients:
  - type: openai-compatible
    name: newapi
    api_base: http://newapi.xifan.icu/v1
    api_key: sk-aukDOsvPQEAUyNQCQo30JirhfYvbruz1qLRprEV4cw3D88kK
    models:
      # OpenAI 模型
      - name: gpt-4o
        max_input_tokens: 128000
        supports_vision: true
        supports_function_calling: true
      - name: gpt-4o-mini
        max_input_tokens: 128000
        supports_function_calling: true
      - name: gpt-4-turbo
        max_input_tokens: 128000
        supports_vision: true
        supports_function_calling: true
      - name: gpt-3.5-turbo
        max_input_tokens: 16385
        supports_function_calling: true

      # Claude 模型
      - name: claude-3-5-sonnet-20241022
        max_input_tokens: 200000
        supports_vision: true
        supports_function_calling: true
      - name: claude-3-5-haiku-20241022
        max_input_tokens: 200000
        supports_function_calling: true
      - name: claude-3-opus-20240229
        max_input_tokens: 200000
        supports_vision: true
        supports_function_calling: true

      # Gemini 模型
      - name: gemini-1.5-pro
        max_input_tokens: 1000000
        supports_vision: true
        supports_function_calling: true
      - name: gemini-1.5-flash
        max_input_tokens: 1000000
        supports_vision: true
        supports_function_calling: true

      # 其他常见模型
      - name: deepseek-chat
        max_input_tokens: 32768
        supports_function_calling: true
      - name: qwen-turbo
        max_input_tokens: 8192
        supports_function_calling: true
  