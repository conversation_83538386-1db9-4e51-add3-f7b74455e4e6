# see https://github.com/sigoden/aichat/blob/main/config.example.yaml

# ---- llm ----
model: newapi:gpt-4o  # 指定默认使用的模型，格式为 客户端名称:模型名称

# ---- behavior ----
stream: true          # 启用流式输出
save: true           # 保存对话历史

clients:
  - type: openai-compatible
    name: newapi
    api_base: http://newapi.xifan.icu/v1
    api_key: sk-aukDOsvPQEAUyNQCQo30JirhfYvbruz1qLRprEV4cw3D88kK
    models:
      # 聊天模型 - 根据你的 newapi 实际支持的模型进行配置
      - name: gpt-4o
        max_input_tokens: 128000
        supports_vision: true
        supports_function_calling: true
      - name: gpt-4o-mini
        max_input_tokens: 128000
        supports_function_calling: true
      - name: gpt-3.5-turbo
        max_input_tokens: 16385
        supports_function_calling: true
      - name: claude-3-5-sonnet-20241022
        max_input_tokens: 200000
        supports_vision: true
        supports_function_calling: true
      - name: claude-3-5-haiku-20241022
        max_input_tokens: 200000
        supports_function_calling: true
      # 如果你的 newapi 支持嵌入模型，可以添加
      # - name: text-embedding-3-small
      #   type: embedding
      #   default_chunk_size: 1500
      #   max_batch_size: 100
  