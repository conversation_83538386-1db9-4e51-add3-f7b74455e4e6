
# see https://github.com/sigoden/aichat/blob/main/config.example.yaml
model: newapi:deepseek-r1  # 指定默认模型

clients:
  - type: openai-compatible
    name: newapi
    api_base: http://newapi.xifan.icu/v1
    api_key: sk-aukDOsvPQEAUyNQCQo30JirhfYvbruz1qLRprEV4cw3D88kK
    models:
      # Qwen 系列
      - name: Qwen/Qwen3-235B-A22B-Instruct-2507
      - name: Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8

      # Claude 系列
      - name: claude-sonnet-4-20250514

      # DeepSeek 系列
      - name: deepseek-ai/DeepSeek-R1
      - name: deepseek-ai/DeepSeek-R1-0528
      - name: deepseek-ai/DeepSeek-V3
      - name: deepseek-ai/DeepSeek-V3-0324
      - name: deepseek-r1
      - name: deepseek-v3

      # Gemini 系列
      - name: gemini-1.5-flash
      - name: gemini-1.5-flash-8b
      - name: gemini-1.5-flash-latest
      - name: gemini-1.5-pro
      - name: gemini-1.5-pro-latest
      - name: gemini-2.0-flash
      - name: gemini-2.0-flash-exp
      - name: gemini-2.0-flash-lite-preview
      - name: gemini-2.0-flash-thinking-exp
      - name: gemini-2.0-pro-exp
      - name: gemini-2.5-flash
      - name: gemini-2.5-pro
      - name: gemini-2.5-pro-exp-03-25
      - name: gemini-2.5-pro-preview-03-25
      - name: gemini-exp-1206

      - name: zai-org/GLM-4.5
      - name: zai-org/GLM-4.5-Air

      # Moonshot 系列
      - name: moonshotai/Kimi-K2-Instruct
  